#!/usr/bin/env python3
"""
简化版Unreal MCP服务器
用于测试与虚幻引擎的TCP连接，不依赖MCP库
"""

import socket
import json
import logging
import sys
import time
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_unreal_server.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("SimpleUnrealServer")

# 虚幻引擎连接配置
UNREAL_HOST = "127.0.0.1"
UNREAL_PORT = 55557

class SimpleUnrealConnection:
    """简单的虚幻引擎连接类"""
    
    def __init__(self):
        self.socket = None
        self.connected = False
    
    def connect(self) -> bool:
        """连接到虚幻引擎"""
        try:
            if self.socket:
                self.socket.close()
            
            logger.info(f"正在连接虚幻引擎 {UNREAL_HOST}:{UNREAL_PORT}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)  # 5秒超时
            self.socket.connect((UNREAL_HOST, UNREAL_PORT))
            self.connected = True
            logger.info("成功连接到虚幻引擎!")
            return True
            
        except Exception as e:
            logger.error(f"连接虚幻引擎失败: {e}")
            self.connected = False
            return False
    
    def send_command(self, command_type: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送命令到虚幻引擎"""
        if not self.connected:
            if not self.connect():
                return {"status": "error", "error": "无法连接到虚幻引擎"}
        
        try:
            # 构建命令
            command = {
                "type": command_type,
                "params": params or {}
            }
            
            # 发送命令
            command_json = json.dumps(command) + "\n"
            logger.info(f"发送命令: {command_json.strip()}")
            self.socket.send(command_json.encode('utf-8'))
            
            # 接收响应 - 改进的JSON接收逻辑
            response_data = ""
            brace_count = 0
            in_json = False

            while True:
                chunk = self.socket.recv(1024).decode('utf-8')
                if not chunk:
                    break

                response_data += chunk

                # 计算大括号来确定JSON是否完整
                for char in chunk:
                    if char == '{':
                        brace_count += 1
                        in_json = True
                    elif char == '}':
                        brace_count -= 1
                        if in_json and brace_count == 0:
                            # JSON完整，停止接收
                            break

                if in_json and brace_count == 0:
                    break

            # 解析响应
            response_data = response_data.strip()
            logger.info(f"收到响应: {response_data[:200]}...")  # 只显示前200个字符

            if response_data:
                try:
                    return json.loads(response_data)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                    return {"status": "error", "error": f"JSON解析错误: {e}"}
            else:
                return {"status": "error", "error": "未收到响应"}
                
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            self.connected = False
            return {"status": "error", "error": str(e)}
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
        self.connected = False

def test_basic_commands():
    """测试基本命令"""
    conn = SimpleUnrealConnection()
    
    try:
        # 测试ping命令
        logger.info("=== 测试ping命令 ===")
        result = conn.send_command("ping")
        logger.info(f"Ping结果: {result}")
        
        # 测试创建立方体
        logger.info("=== 测试创建立方体 ===")
        result = conn.send_command("spawn_actor", {
            "type": "cube",
            "name": "TestCube",
            "location": [0, 0, 100]
        })
        logger.info(f"创建立方体结果: {result}")

        # 测试列出所有Actor
        logger.info("=== 测试列出所有Actor ===")
        result = conn.send_command("get_actors_in_level")
        logger.info(f"Actor列表: {result}")
        
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    finally:
        conn.close()
        logger.info("连接已关闭")

def interactive_mode():
    """交互模式"""
    conn = SimpleUnrealConnection()
    
    print("=== 简化版虚幻引擎服务器 ===")
    print("可用命令:")
    print("  ping - 测试连接")
    print("  cube - 创建立方体")
    print("  sphere - 创建球体")
    print("  list - 列出所有Actor")
    print("  quit - 退出")
    print()
    
    try:
        while True:
            command = input("请输入命令: ").strip().lower()
            
            if command == "quit":
                break
            elif command == "ping":
                result = conn.send_command("ping")
                print(f"结果: {result}")
            elif command == "cube":
                result = conn.send_command("spawn_actor", {
                    "type": "cube",
                    "name": f"Cube_{int(time.time())}",
                    "location": [0, 0, 100]
                })
                print(f"结果: {result}")
            elif command == "sphere":
                result = conn.send_command("spawn_actor", {
                    "type": "sphere",
                    "name": f"Sphere_{int(time.time())}",
                    "location": [100, 0, 100]
                })
                print(f"结果: {result}")
            elif command == "list":
                result = conn.send_command("get_actors_in_level")
                print(f"结果: {result}")
            else:
                print("未知命令，请重试")
                
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        conn.close()
        print("连接已关闭")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_basic_commands()
    else:
        interactive_mode()
